<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border show-summary :summary-method="calculateSummaries">
      <el-table-column label="序号" type="index" width="60" />
       <el-table-column label="产品名称" min-width="240">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productName`" :rules="formRules.productName" class="mb-0px!">
             <ScrollSelect
              v-if="row.productId || !row.productName"
              filterable
              clearable
              class="!w-240px"
              v-model="row.productId"
              :load-method="getRemoteMaterial"
              :label-key="formatMaterialLabel"
              value-key="id"
              :default-value="{id: row.productId, name: row.productName, fullCode: row.productCode, spec: row.spec}"
              query-key="name"
              :extra-params="{ 'types': ([2])}"
              @change="(val, material) => {
                row.productName = material?.name
                row.productCode = material?.fullCode
                row.unit = Number(material?.saleUnit)
                row.spec = material?.spec
                row.productSource = material?.source
              }"
            />
            <!-- 当产品ID不存在但有产品名称时，显示为只读文本，点击时切换为选择模式 -->
            <el-input
              v-else-if="!row.productId && row.productName"
              v-model="row.productName"
              placeholder="产品名称"
              readonly
              class="!w-240px cursor-pointer"
              @click="convertToSelectMode(row)"
              title="点击切换为选择模式"
            />
          
        </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="产品编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.productCode`" :rules="formRules.productCode" class="mb-0px!">
            <el-input v-model="row.productCode" placeholder="订单商品编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="规格" min-width="190">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.spec`" :rules="formRules.spec" class="mb-0px!">
            <el-select
              v-model="row.spec"
              placeholder="请选择规格"
              class="!w-160px"
              filterable
              clearable
              disabled
            >
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PROD_SPEC)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
  
      <el-table-column label="数量" min-width="120" prop="quantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input v-model="row.quantity" placeholder="数量" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit as number"
              placeholder="请选择单位"
              class="!w-150px"
              value-key="id"
              filterable
              clearable
              disabled
            >
              <el-option v-for="unit in unitList" :key="unit.id" :label="unit.name" :value="unit.id" />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="规格数量" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.specQuantityTotal`" :rules="formRules.specQuantityTotal" class="mb-0px!">
            <el-input v-model="row.specQuantityTotal" placeholder="规格数量"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税单价" min-width="120" prop="unitPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input v-model="row.unitPrice" placeholder="含税单价" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="不含税单价" min-width="120" prop="noTaxUnitPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.noTaxUnitPrice`" :rules="formRules.noTaxUnitPrice" class="mb-0px!">
            <el-input v-model="row.noTaxUnitPrice" placeholder="不含税单价" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="辅助单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.auxiliaryUnit`" :rules="formRules.auxiliaryUnit" class="mb-0px!">
            <el-input v-model="row.auxiliaryUnit" placeholder="请输入辅助单位" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="税率%" min-width="135">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxRate`" :rules="formRules.taxRate" class="mb-0px!">
            <el-input v-model="row.taxRate" placeholder="税率" type="number">
              <template #append>%</template>
            </el-input>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税总价" min-width="120" prop="total">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.total`" :rules="formRules.total" class="mb-0px!">
            <el-input v-model="row.total" placeholder="含税总价" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="不含税总价" min-width="120" prop="noTaxTotal" >
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.noTaxTotal`" :rules="formRules.noTaxTotal" class="mb-0px!">
            <el-input v-model="row.noTaxTotal" placeholder="不含税总价" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      
      <el-table-column label="税额" min-width="120" prop="tax">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.tax`" :rules="formRules.tax" class="mb-0px!">
            <el-input v-model="row.tax" placeholder="税额" type="number"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="交货日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.deliveryDate`" :rules="formRules.deliveryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.deliveryDate"
              type="date"
              value-format="x"
              placeholder="选择交货日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
     
      <el-table-column label="订单要求" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.requirement`" :rules="formRules.requirement" class="mb-0px!">
            <el-input v-model="row.requirement" placeholder="请输入订单要求" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link type="danger">—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
      
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加订单产品</el-button>
  </el-row>
</template>
<script setup lang="ts">

import { OrderApi } from '@/api/scm/sale/order'
import { MaterialApi, MaterialVO } from '@/api/scm/base/material'
import { UnitApi, UnitVO } from '@/api/scm/base/unit'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
import { formatAmount, formatQuantity } from '@/utils/formatter'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  calculateSpecQuantityWithRemainder,
  parseSpecification,
  getUnitConversionFactor
} from '@/utils/bomCalculation'


const props = defineProps<{
  orderId: undefined // 订单ID（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const isFromQuote = ref(false) // 标记是否来自报价单填充
const formRules = reactive({
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  productCode: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
  unitPrice: [{ required: true, message: '含税单价不能为空', trigger: 'blur' }],
  noTaxUnitPrice: [{ required: true, message: '不含税单价不能为空', trigger: 'blur' }],
  unit: [{ required: true, message: '单位不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
  specQuantityTotal: [{ required: false, message: '规格数量不能为空', trigger: 'blur' }],
  taxRate: [{ required: true, message: '税率不能为空', trigger: 'blur' }],
  total: [{ required: true, message: '含税总价不能为空', trigger: 'blur' }],
  noTaxTotal: [{ required: true, message: '不含税总价不能为空', trigger: 'blur' }],
  tax: [{ required: true, message: '税额不能为空', trigger: 'blur' }],
  deliveryDate: [{ required: true, message: '交货日期不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.orderId,
  async (val) => {
    // 如果是从报价单填充的数据，不要重置
    if (isFromQuote.value) {
      isFromQuote.value = false
      return
    }

    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      let res = await OrderApi.getOrderDetailListByOrderId({}, val)
      console.log('子表数据', res)
      // 新增：预加载已选择的产品数据
      const productIds = res.list.map(item => item.productId).filter(Boolean)
      if (productIds.length > 0) {
        const productRes = await MaterialApi.getSimpleMaterialPage({ ids: productIds, pageSize: 100 })
        remoteMaterials.value = [...remoteMaterials.value, ...productRes.list] // 合并原有和新增数据
      }
      formData.value = res.list
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = reactive({
    orderDetailId: undefined,
    orderId: undefined,
    productName: undefined,
    orderNo: undefined,
    productCode: undefined,
    productId: undefined,
    deliveredQuantity: undefined,
    status: undefined,
    productionStatus: undefined,
    invoiceStatus: undefined,
    specQuantity: undefined,
    quantity: undefined,
    unit: undefined,
    specQuantityTotal: undefined,
    unitPrice: undefined,
    noTaxUnitPrice: undefined,
    auxiliaryUnit: undefined,
    specUnit: undefined,
    total: undefined,
    taxRate: undefined,
    tax: undefined,
    deliveryDate: undefined,
    noTaxTotal: undefined,
    requirement: undefined,
    remark: undefined,
  })

   // 添加响应式计算
  watchEffect(() => {
    const qty = Number(row.quantity) || 0
    const price = Number(row.unitPrice) || 0
    const noTaxPrice = Number(row.noTaxUnitPrice) || 0
    const rate = Number(row.taxRate) || 0

    // 如果有含税单价，基于含税单价计算
    if (price > 0) {
      const subtotal = qty * price
      const taxRateFactor = 1 + (rate / 100)

      row.total = Number((subtotal).toFixed(2))
      row.tax = Number((subtotal * rate / 100 / taxRateFactor).toFixed(2))
      row.noTaxTotal = Number((subtotal / taxRateFactor).toFixed(2))
      row.noTaxUnitPrice = Number((price / taxRateFactor).toFixed(2))
    }
    // 如果有不含税单价，基于不含税单价计算
    else if (noTaxPrice > 0) {
      const noTaxSubtotal = qty * noTaxPrice
      const taxAmount = noTaxSubtotal * rate / 100

      row.noTaxTotal = Number(noTaxSubtotal.toFixed(2))
      row.tax = Number(taxAmount.toFixed(2))
      row.total = Number((noTaxSubtotal + taxAmount).toFixed(2))
      row.unitPrice = Number((noTaxPrice * (1 + rate / 100)).toFixed(2))
    }

    // 计算规格数量
    row.specQuantityTotal = calculateSpecQuantity(row)
  })
  row.orderId = props.orderId
  formData.value.push(row)
}

/** 转换为选择模式 */
const convertToSelectMode = (row: any) => {
  // 清空产品相关字段，让用户重新选择
  row.productId = undefined
  row.productName = undefined
  row.productCode = undefined
  row.spec = undefined
  row.unit = undefined
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}
// 新增响应式数据
const remoteMaterials = ref<MaterialVO[]>([]) // 存储远程数据

const getRemoteMaterial = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

/** 格式化物料选项显示标签 */
const formatMaterialLabel = (material: MaterialVO) => {
  if (!material) return ''
  const fullCode = material.fullCode || ''
  const name = material.name || ''
  const spec = material.spec || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (fullCode) parts.push(fullCode)
  if (name) parts.push(name)
  if (spec) parts.push(spec)

  return parts.join(' - ')
}

const unitList = ref<UnitVO[]>([]) // 存储单位列表
const getUnitList = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100 })
  unitList.value = data.list
}

/** 计算规格数量 */
const calculateSpecQuantity = (row: any): string => {
  const quantity = Number(row.quantity) || 0
  const spec = row.spec || ''
  const unit = row.unit || 0

  // 如果没有数量或规格，返回空字符串
  if (quantity <= 0 || !spec) {
    return ''
  }

  // 获取单位名称
  let unitName = '个'
  if (unitList.value && unitList.value.length > 0 && unit) {
    const unitObj = unitList.value.find(u => u.id === unit)
    unitName = unitObj?.name || '个'
  }

  // 解析规格信息
  const specInfo = parseSpecification(spec)

  // 获取单位转换信息
  const unitInfo = getUnitConversionFactor(unitName)

  // 使用规格计算函数
  const result = calculateSpecQuantityWithRemainder(quantity, specInfo, unitInfo, unitName)

  // 如果没有余数（能够整除），只显示包装数量，不显示括号中的内容
  if (result.remainder === 0 && result.packages > 0) {
    return `${result.packages}${specInfo.packageUnit}`
  }

  return result.displayText
}
// 汇总方法改造
const calculateSummaries = ({ columns }) => {
  const sums: any[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    const key = column.property
    if (key === 'quantity') {
      // 按单位分组统计
      const quantityByUnit = formData.value.reduce((acc, row) => {
        const unitId = row.unit
        if (!unitId) return acc
        const unit = unitList.value.find(u => u.id === unitId)
        const unitName = unit?.name || `单位[${unitId}]`
        acc[unitName] = (acc[unitName] || 0) + (Number(row.quantity) || 0)
        return acc
      }, {})

      // 生成换行文本（使用 \n）
      const lines = Object.entries(quantityByUnit)
        .map(([unit, sum]) => `${formatQuantity(Number(sum))}${unit}`)
      const total = Object.values(quantityByUnit)
        .reduce((acc, sum) => acc + Number(sum), 0)
      // lines.push(`合计: ${total}`) // 添加总合计

      sums[index] = lines.join('\n') // 用换行符连接
    } 
    // 金额字段汇总
    else if (['total', 'noTaxTotal', 'tax'].includes(key)) {
      const sum = formData.value
        .map(row => Number(row[key]) || 0)
        .reduce((acc, curr) => acc + curr, 0)
      sums[index] = formatAmount(sum)
    }
    // 单价字段不汇总，显示为空
    else if (['unitPrice', 'noTaxUnitPrice'].includes(key)) {
      sums[index] = ''
    }
    // 其他字段不汇总
    else {
      sums[index] = ''
    }
  })
  return sums
}

/** 从报价单添加订单产品明细 */
const addOrderDetailFromQuote = (quoteData: any) => {
  // 设置标志，防止watch重置数据
  isFromQuote.value = true

  // 清空现有数据
  formData.value = []

  // 创建新的订单明细行
  const row = reactive({
    orderDetailId: undefined,
    orderId: quoteData.orderId,
    productName: quoteData.productName,
    orderNo: undefined,
    productCode: quoteData.productCode,
    productId: quoteData.productId,
    deliveredQuantity: undefined,
    status: undefined,
    productionStatus: undefined,
    invoiceStatus: undefined,
    specQuantity: undefined,
    quantity: quoteData.quantity,
    unit: quoteData.unit,
    specQuantityTotal: undefined,
    unitPrice: quoteData.unitPrice,
    noTaxUnitPrice: undefined,
    auxiliaryUnit: undefined,
    specUnit: undefined,
    total: undefined,
    taxRate: 13, // 默认税率13%
    tax: undefined,
    deliveryDate: undefined,
    noTaxTotal: undefined,
    requirement: quoteData.requirement,
    remark: undefined,
    spec: quoteData.spec,
    productSource: quoteData.productSource
  })


  // 添加响应式计算
  watchEffect(() => {
    const qty = Number(row.quantity) || 0
    const price = Number(row.unitPrice) || 0
    const noTaxPrice = Number(row.noTaxUnitPrice) || 0
    const rate = Number(row.taxRate) || 0

    // 如果有含税单价，基于含税单价计算
    if (price > 0) {
      const subtotal = qty * price
      const taxRateFactor = 1 + (rate / 100)

      row.total = Number((subtotal).toFixed(2))
      row.tax = Number((subtotal * rate / 100 / taxRateFactor).toFixed(2))
      row.noTaxTotal = Number((subtotal / taxRateFactor).toFixed(2))
      row.noTaxUnitPrice = Number((price / taxRateFactor).toFixed(2))
    }
    // 如果有不含税单价，基于不含税单价计算
    else if (noTaxPrice > 0) {
      const noTaxSubtotal = qty * noTaxPrice
      const taxAmount = noTaxSubtotal * rate / 100

      row.noTaxTotal = Number(noTaxSubtotal.toFixed(2))
      row.tax = Number(taxAmount.toFixed(2))
      row.total = Number((noTaxSubtotal + taxAmount).toFixed(2))
      row.unitPrice = Number((noTaxPrice * (1 + rate / 100)).toFixed(2))
    }

    // 计算规格数量
    row.specQuantityTotal = calculateSpecQuantity(row)
  })

  formData.value.push(row)
}

defineExpose({ validate, getData, addOrderDetailFromQuote })

onMounted(() => {
  // 1. 获取单位列表
  getUnitList()
})

</script>
<style lang="scss" scoped>
  /* 让汇总行支持换行 */
  :deep(.el-table__footer) .cell {
    padding: 8px 0;
    line-height: 1.5;
    white-space: pre-line;
  }
</style>
