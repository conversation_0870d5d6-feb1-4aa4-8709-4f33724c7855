<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border max-height="300px" show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="序号" min-width="60" prop="num" align="center"/>

      <el-table-column label="物料信息" min-width="300">
        <template #default="{ row }">
          <div class="material-source-info">
            <div class="material-section">
              <div class="material-name">
                <span class="label">名称：</span>
                <span class="value">{{ row.materialName || '-' }}</span>
              </div>
              <div class="material-details">
                <span class="label">编码：</span>
                <span class="value">{{ row.materialCode || '-' }}</span>
                <span class="separator">|</span>
                <span class="label">规格：</span>
                <span class="value">{{ row.materialSpec || '-' }}</span>
              </div>
            </div>
            <div class="source-section">
              <span class="label">源单：</span>
              <span class="value">{{ row.sourceNo || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
<!--      <el-table-column label="物料规格" min-width="150">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.materialSpec`" :rules="formRules.materialSpec" class="mb-0px!">-->
<!--            <el-input v-model="row.materialSpec" placeholder="请输入物料规格" disabled/>-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="单位" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              clearable
              class="!w-130px"
              disabled
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="单价" min-width="150" prop="unitPrice">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.unitPrice"-->
<!--              placeholder="请输入单价"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="金额" min-width="150" prop="amount">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.amount"-->
<!--              placeholder="请输入金额"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column label="仓库" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-tree-select
              v-model="row.warehouseId"
              :data="warehouseTreeData"
              placeholder="请选择仓库"
              clearable
              filterable
              check-strictly
              :render-after-expand="false"
              class="!w-130px"
              node-key="id"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="(value) => handleWarehouseChange($index, value)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应领数量" min-width="150" prop="plannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">
            <el-input
              v-model="row.plannedQuantity"
              placeholder="请输入应领数量"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="实领数量" min-width="150" prop="fulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">
            <el-input
              v-model="row.fulfilledQuantity"
              placeholder="请输入实领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="基本单位应领数量" min-width="150" prop="standardPlannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardPlannedQuantity"
              placeholder="请输入基本单位应领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位实领数量" min-width="150" prop="standardFulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardFulfilledQuantity"
              placeholder="请输入基本单位实领数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">
            <el-select
              v-model="row.standardUnit"
              placeholder="请选择基本单位"
              clearable
              class="!w-130px"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.name"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税单价" min-width="150" prop="taxPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" :rules="formRules.taxPrice" class="mb-0px!">
            <el-input
              v-model="row.taxPrice"
              placeholder="请输入含税单价"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税金额" min-width="150" prop="taxAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input
              v-model="row.taxAmount"
              placeholder="请输入含税金额"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" min-width="150" prop="invoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceQuantity`" :rules="formRules.invoiceQuantity" class="mb-0px!">
            <el-input
              v-model="row.invoiceQuantity"
              placeholder="请输入开票数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票金额" min-width="150" prop="invoiceAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceAmount`" :rules="formRules.invoiceAmount" class="mb-0px!">
            <el-input
              v-model="row.invoiceAmount"
              placeholder="请输入开票金额"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票基本数量" min-width="150" prop="standardInvoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardInvoiceQuantity`" :rules="formRules.standardInvoiceQuantity" class="mb-0px!">
            <el-input
              v-model="row.standardInvoiceQuantity"
              placeholder="请输入开票基本数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="生产日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.effictiveDate`" :rules="formRules.effictiveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.effictiveDate"
              type="date"
              value-format="x"
              placeholder="选择生产日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="失效日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expiryDate`" :rules="formRules.expiryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="x"
              placeholder="选择失效日期"
            />
          </el-form-item>
        </template>
      </el-table-column>-->
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
     <el-table-column label="批号" min-width="230">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-select
              v-model="row.batchNo"
              placeholder="请选择批号"
              clearable
              filterable
              class="!w-200px"
              :disabled="!row.materialId"
            >
              <el-option
                v-for="batch in row.batchOptions || []"
                :key="batch.id"
                :label="batch.label"
                :value="batch.value"
                :disabled="batch.disabled"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="成本对象编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectId`" :rules="formRules.costObjectId" class="mb-0px!">
            <el-input v-model="row.costObjectId" placeholder="请输入成本对象编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">
            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="记账凭证号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.accountingVoucherNumber`" :rules="formRules.accountingVoucherNumber" class="mb-0px!">
            <el-input v-model="row.accountingVoucherNumber" placeholder="请输入记账凭证号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="库位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select
              v-model="row.locationId"
              placeholder="请选择库位"
              clearable
              filterable
              class="!w-130px"
            >
              <el-option
                v-for="location in getFilteredLocationList(row.warehouseId)"
                :key="location.id"
                :label="location.name"
                :value="location.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link>—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加领料出库明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PickingReceiptApi } from '@/api/scm/inventory/pickingreceipt'
import { InventoryDetail } from '@/types/inventory'
import { UnitApi } from '@/api/scm/base/unit';
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import { formatAmount, formatQuantity } from '@/utils/formatter';
import {
  mountBatchOptionsToRows,
  validateBatchData,
  formatBatchLabel,
  type BatchDataByMaterial
} from '@/utils/batchUtils'


const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  },
  materialType: {
    type: String,
    default: 'raw', // 'raw' 原料, 'package' 包材
    validator: (value: string) => ['raw', 'package'].includes(value)
  },
  sharedData: {
    type: Object,
    default: () => ({
      warehouseList: [],
      warehouseTreeData: [],
      unitList: [],
      locationList: [],
      loading: false
    })
  }
})
const formLoading = ref(false) // 表单的加载中
const formData = ref<InventoryDetail[]>([])
const formRules = reactive<any>({
})


const formRef = ref() // 表单 Ref
// 使用计算属性从共享数据中获取数据，避免重复请求
const locationList = computed(() => props.sharedData?.locationList || [])
const unitList = computed(() => props.sharedData?.unitList || [])
const warehouseTreeData = computed(() => props.sharedData?.warehouseTreeData || [])

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.bizOrderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const details = await PickingReceiptApi.getPickingReceiptDetailListByBizOrderId(val)
      
      // 为每行添加批次选项字段并批量加载批次数据
      details.forEach(row => {
        row.batchOptions = []
      })

      // 批量加载所有物料的批次信息
      await loadBatchOptionsForMultipleMaterials(details)
      
      formData.value = details
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)
watch(
  () => props.warehouseId,
  async (val) => {
    formData.value = formData.value.map((item:InventoryDetail) => {
      item.warehouseId = val
      return item
    })
  }
)

/** 获取单位列表 - 已移除，使用父组件共享数据 */

// 防重复调用的标记和缓存
let isLoadingBatch = false
let lastLoadedMaterialIds: number[] = []

/** 批量加载批次选项（多个物料） */
const loadBatchOptionsForMultipleMaterials = async (rows: any[]) => {
  // 防止重复调用
  if (isLoadingBatch) {
    return
  }

  // 收集所有需要加载批次的物料ID
  const materialIds = rows
    .filter(row => row.materialId)
    .map(row => Number(row.materialId))
    .filter((id, index, arr) => arr.indexOf(id) === index) // 去重

  if (materialIds.length === 0) {
    // 如果没有物料ID，清空所有行的批次选项
    rows.forEach(row => {
      row.batchOptions = []
      row.batchNo = undefined
    })
    return
  }

  // 检查是否与上次加载的物料ID相同
  const materialIdsStr = materialIds.sort().join(',')
  const lastMaterialIdsStr = lastLoadedMaterialIds.sort().join(',')
  if (materialIdsStr === lastMaterialIdsStr) {
    return
  }

  isLoadingBatch = true
  lastLoadedMaterialIds = [...materialIds]

  try {
    // 使用修改后的API，它会正确处理数组参数格式
    const response = await BatchInfoApi.getSimpleBatchInfoListByMaterialIds({
      materialIds: materialIds
    })

    // 验证批次数据
    const validation = validateBatchData(response as BatchDataByMaterial)
    if (!validation.isValid) {
      validation.errors.forEach(error => console.error(error))
    }
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => console.warn(warning))
    }

    // 使用工具类挂载批次选项到行数据
    mountBatchOptionsToRows(rows, response as BatchDataByMaterial, true)
  } catch (error) {
    console.error('批量加载批次信息失败:', error)
    // 出错时使用工具类清空所有行的批次选项
    mountBatchOptionsToRows(rows, {}, false)
  } finally {
    isLoadingBatch = false
  }
}



// formatBatchLabel 函数已移至 @/utils/batchUtils 工具类中

//初始化方法
onMounted(async () => {
  // 不再需要重复加载仓库、单位、库位数据，这些数据由父组件提供
  // onMounted时不加载批次信息，避免与其他地方的加载重复
  // 批次信息的加载由业务订单ID监听器或setData方法处理
})

/** 新增按钮操作 */
const handleAdd = () => {
  // 根据物料类型设置默认仓库ID
  let defaultWarehouseId = props.warehouseId

  const row:InventoryDetail = {
    id: undefined,
    num: undefined,
    bizOrderId: undefined,
    bizOrderNo: undefined,
    warehouseId: defaultWarehouseId,
    locationId: undefined,
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    materialSpec: undefined,
    unit: undefined,
    unitPrice: undefined,
    amount: undefined,
    remark: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    taxPrice: undefined,
    taxAmount: undefined,
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,
    effictiveDate: undefined,
    expiryDate: undefined,
    note: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    batchNo: undefined,
    batchOptions: [], // 添加批次选项
    costObjectId: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
  }
  row.warehouseId = props.warehouseId
  row.bizOrderId = props.bizOrderId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 库位信息相关方法 - 已移除，使用父组件共享数据 */

/** 表单值 */
const getData = () => {
  // 过滤掉batchOptions字段，避免传递给后端
  return formData.value.map(item => {
    const { batchOptions, ...rest } = item
    return rest
  })
}


/** 设置表单数据 */
const setData = async (data: any[]) => {
  if (!data || data.length === 0) {
    formData.value = []
    return
  }

  // 根据materialType和materialCode过滤数据
  let filteredData = data
  if (props.materialType === 'raw') {
    // 原料：materialCode不以"4"开头
    filteredData = data.filter(item => {
      const materialCode = item.materialCode || ''
      return !materialCode.startsWith('4')
    })
  } else if (props.materialType === 'package') {
    // 包材：materialCode以"4"开头
    filteredData = data.filter(item => {
      const materialCode = item.materialCode || ''
      return materialCode.startsWith('4')
    })
  }

  // 单位数据由父组件提供，无需重复加载

  // 处理每一行数据，转换单位信息
  const processedData = await Promise.all(filteredData.map(async (item) => {
    const processedItem = { ...item }

    // 如果 unit 字段存在且是数字（单位ID），需要确保选择器能正确显示
    if (processedItem.unit) {
      const unitId = typeof processedItem.unit === 'string' ? parseInt(processedItem.unit) : processedItem.unit

      // 在单位列表中查找对应的单位
      const foundUnit = unitList.value.find((u: any) => u.id === unitId)
      if (foundUnit) {
        processedItem.unit = foundUnit.id // 保持单位ID，与选择器的value类型一致
        processedItem.unitName = foundUnit.name
      } else {
        // 如果在本地列表中找不到，尝试从API获取
        try {
          const unitInfo = await UnitApi.getUnit(unitId)
          if (unitInfo) {
            processedItem.unit = unitInfo.id // 保持单位ID
            processedItem.unitName = unitInfo.name
          }
        } catch (error) {
          // 获取单位信息失败时忽略错误
        }
      }
    }

    // 初始化批次选项字段
    processedItem.batchOptions = []

    return processedItem
  }))

  // 批量加载所有物料的批次信息
  await loadBatchOptionsForMultipleMaterials(processedData)

  formData.value = processedData
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['plannedQuantity', 'fulfilledQuantity', 'standardPlannedQuantity',
                           'standardFulfilledQuantity', 'invoiceQuantity', 'standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['unitPrice', 'amount', 'taxPrice', 'taxAmount', 'invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

/** 初始化仓库数据 - 已移除，使用父组件共享数据 */



// 根据仓库ID过滤库位列表
const getFilteredLocationList = (warehouseId: any) => {
  const locations = locationList.value
  // 确保 locations 是数组
  if (!Array.isArray(locations)) {
    return []
  }

  if (!warehouseId) {
    return locations
  }

  // 确保每个 location 对象都有 warehouseId 属性
  return locations.filter(location => {
    if (!location || typeof location !== 'object') {
      return false
    }
    return location.warehouseId === warehouseId
  })
}

// 处理仓库选择变化
const handleWarehouseChange = (index: number, warehouseId: any) => {
  // 清空库位选择
  formData.value[index].locationId = undefined

  // 如果主表没有选择仓库，则更新主表仓库
  if (warehouseId && !props.warehouseId) {
    // 这里可以触发事件通知父组件更新主表仓库
    // emit('warehouse-change', warehouseId)
  }
}

defineExpose({ validate, getData, setData })
</script>

<style lang="scss" scoped>
.material-source-info {
  .material-section {
    margin-bottom: 6px;

    .material-details {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 1.4;

      .label {
        color: #909399;
        font-weight: 500;
        margin-right: 4px;
      }

      .value {
        color: #606266;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
      }

      .separator {
        color: #dcdfe6;
        margin: 0 6px;
      }
    }
  }

  .source-section,
  .material-name{
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 1.4;

    .label {
      color: #909399;
      font-weight: 500;
      min-width: 36px;
      flex-shrink: 0;
    }

    .value {
      color: #606266;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
