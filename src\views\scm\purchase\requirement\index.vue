<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <!-- 始终显示的基础搜索项 -->
      <el-form-item label="需求编号" prop="requestNo">
        <el-input
          v-model="queryParams.requestNo"
          placeholder="请输入需求编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料编号" prop="materialCode">
        <el-input
          v-model="queryParams.materialCode"
          placeholder="请输入物料编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <!-- 高级搜索项 - 可展开收起 -->
      <template v-if="isExpanded">
        <el-form-item label="需求日期" prop="requirementDate">
          <el-date-picker
            v-model="queryParams.requirementDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_REQ_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物料规格" prop="materialSpec">
          <el-input
            v-model="queryParams.materialSpec"
            placeholder="请输入物料规格"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="物料类型" prop="materialType">
          <el-select
            v-model="queryParams.materialType"
            placeholder="请选择物料类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.MATERIAL_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="交货日期" prop="expectedDeliveryDate">
          <el-date-picker
            v-model="queryParams.expectedDeliveryDate"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="来源类型" prop="sourceType">
          <el-select
            v-model="queryParams.sourceType"
            placeholder="请选择需求来源类型"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_REQ_SOURCE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源单号" prop="sourceNo">
          <el-input
            v-model="queryParams.sourceNo"
            placeholder="请输入来源单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="业务类型" prop="bizOrderType">
          <el-input
            v-model="queryParams.bizOrderType"
            placeholder="请输入业务订单类型"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-240px"
          />
        </el-form-item>
      </template>

      <!-- 操作按钮行 -->
      <el-form-item>
        <el-button @click="handleQuery" class="ml-4"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['purchase:requirement:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="primary"
          plain
          @click="handleTransfer"
          v-hasPermi="['purchase:requirement:transfer']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 转采购单
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['purchase:requirement:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button
          type="text"
          @click="toggleExpanded"
          class="ml-2"
        >
          {{ isExpanded ? '收起' : '展开' }}
          <Icon :icon="isExpanded ? 'ep:arrow-up' : 'ep:arrow-down'" class="ml-1" />
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true" 
      show-summary
      :summary-method="summaryMethod" 
      @selection-change="handleSelectionChange"
      :border="true" 
    >
      <!-- <el-table-column label="ID" align="center" prop="id" /> -->
      <el-table-column type="selection" width="60px" />
      <el-table-column label="需求编号" align="left" prop="requestNo" width="200px">
        <template #default="scope">
          <div class="request-no-container">
            <div class="order-no-cell">
              <div class="order-no-content">
                <span class="request-no-text">{{ scope.row.requestNo }}</span>
              </div>
              <el-button
                link
                type="info"
                @click="copyOrderNo(scope.row.requestNo)"
                class="copy-btn copy-btn-fixed"
                size="small"
              >
                <Icon icon="ep:copy-document" :size="12"/>
              </el-button>
            </div>
            <dict-tag
              v-if="scope.row.status"
              :type="DICT_TYPE.PURCHASE_REQ_STATUS"
              :value="scope.row.status"
              class="status-tag"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" align="center" prop="materialCode" width="120px" sortable/>
      <el-table-column label="物料名称" align="center" prop="materialName" width="150px"/>
      <el-table-column
        label="需求日期"
        align="center"
        prop="requirementDate"
        :formatter="dateFormatter2"
        width="100px"
      />

      <el-table-column label="物料规格" align="center" prop="materialSpec"  width="100"/>
      <el-table-column label="物料类型" align="center" prop="materialType" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.MATERIAL_TYPE" :value="scope.row.materialType" />
        </template>
      </el-table-column>
      <el-table-column label="需求部门" align="center" width="120">
        <template #default="{ row }">
          {{ deptMap.get(Number(row.departmentId)) || row.departmentName || '' }}
        </template>
      </el-table-column>
      <el-table-column label="需求数量" align="center" prop="quantity" width="120">
        <template #default="scope">
          {{ formatQuantity(scope.row.quantity) }} {{ unitMap.get(Number(scope.row.unit)) }}
        </template>
      </el-table-column>
      <el-table-column
        label="交货日期"
        align="center"
        prop="expectedDeliveryDate"
        :formatter="dateFormatter2"
        width="100px"
      />

      <el-table-column label="来源类型" align="center" prop="sourceType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PURCHASE_REQ_SOURCE" :value="scope.row.sourceType" />
        </template>
      </el-table-column>
      <el-table-column label="来源单号" align="center" prop="sourceNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.sourceNo">
            <div class="order-no-content">
              <span>{{ scope.row.sourceNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.sourceNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="bizOrderType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SCM_BIZ_TYPE" :value="scope.row.bizOrderType" />
        </template>
      </el-table-column>
      <el-table-column label="业务单号" align="center" prop="bizOrderNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.bizOrderNo">
            <div class="order-no-content">
              <span>{{ scope.row.bizOrderNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.bizOrderNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="摘要" align="center" prop="note" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="采购订单" align="center" prop="purchaseNo" width="120">
        <template #default="scope">
          <div class="order-no-cell" v-if="scope.row.purchaseNo">
            <div class="order-no-content">
              <span>{{ scope.row.purchaseNo }}</span>
            </div>
            <el-button
              link
              type="info"
              @click="copyOrderNo(scope.row.purchaseNo)"
              class="copy-btn copy-btn-fixed"
              size="small"
            >
              <Icon icon="ep:copy-document" :size="12"/>
            </el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="180px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openPurchaseOrderForm('create', scope.row)"
            v-hasPermi="['purchase:requirement:transfer']"
            v-if="scope.row.status !== '1'"
          >
            下采购单
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['purchase:requirement:update']"
            v-if="scope.row.status == '0'"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['purchase:requirement:delete']"
            v-if="scope.row.status == '0'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RequirementForm ref="formRef" @success="getList" />
  <PurchaseOrderForm ref="purchaseOrderFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, dateFormatter2} from '@/utils/formatTime'
import download from '@/utils/download'
import { RequirementApi, RequirementVO } from '@/api/scm/purchase/requirement'
import { formatQuantity } from '@/utils/formatter'
import RequirementForm from './RequirementForm.vue'
import PurchaseOrderForm from '../order/OrderForm.vue'
import { UnitApi } from '@/api/scm/base/unit'
import {getRemoteUnitMap} from '@/utils/commonBiz'
import * as DeptApi from '@/api/system/dept'
import { useClipboard } from '@vueuse/core'

/** 采购需求 列表 */
defineOptions({ name: 'Requirement' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { copy } = useClipboard() // 复制功能

/** 复制订单单号 */
const copyOrderNo = async (orderNo: string) => {
  try {
    await copy(orderNo)
    message.success('单号复制成功')
  } catch (error) {
    message.error('复制失败')
  }
}

const loading = ref(true) // 列表的加载中
const list = ref<RequirementVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const isExpanded = ref(false) // 搜索表单展开状态
const selectedRequirements = ref<RequirementVO[]>([]) // 选中的需求单
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  requestNo: undefined,
  materialId: undefined,
  materialName: undefined,
  requirementDate: [],
  materialCode: undefined,
  materialSpec: undefined,
  materialType: undefined,
  departmentId: undefined,
  quantity: undefined,
  unitPrice: undefined,
  unit: undefined,
  amount: undefined,
  expectedDeliveryDate: [],
  status: undefined,
  totalAmount: undefined,
  sourceType: undefined,
  sourceNo: undefined,
  bizOrderId: undefined,
  bizOrderType: undefined,
  remark: undefined,
  note: undefined,
  createTime: [],
  purchaseNo: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RequirementApi.getRequirementPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 切换展开/收起状态 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 采购订单添加订单操作 */
const purchaseOrderFormRef = ref()
const openPurchaseOrderForm = (type: string, requirementData: any, id?: number) => {
  // 如果传入的是需求数据，将其作为第三个参数传递给采购订单表单
  console.log(requirementData)
  if (type === 'create' && requirementData && !id) {
    purchaseOrderFormRef.value.open(type, undefined, requirementData)
  } else {
    purchaseOrderFormRef.value.open(type, id, requirementData)
  }
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RequirementApi.deleteRequirement(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RequirementApi.exportRequirement(queryParams)
    download.excel(data, '采购需求.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选择变更处理 */
const handleSelectionChange = (selection: RequirementVO[]) => {
  selectedRequirements.value = selection
}

/** 将选中的需求单传递给采购单form */
const handleTransfer = () => {
  if (selectedRequirements.value.length === 0) {
    message.error('请选择要转换的采购需求')
    return
  }
  console.log('选中的需求单:', selectedRequirements.value)
  // 将所有选中的需求传递给采购单表单
  // 采购单表单会将这些需求合并相同物料后添加到采购明细中
  openPurchaseOrderForm('create', selectedRequirements.value)
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数值字段
    const numericFields = ['quantity', 'unitPrice', 'amount', 'totalAmount']

    if (numericFields.includes(column.property)) {
      // 数值字段汇总 - 简单数字相加
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)

      // 根据字段类型选择格式化方式
      if (column.property === 'quantity') {
        sums[index] = formatQuantity(total)
      } else if (['unitPrice', 'amount', 'totalAmount'].includes(column.property)) {
        // 金额字段使用金额格式化（如果有的话，否则使用数量格式化）
        sums[index] = formatQuantity(total)
      } else {
        sums[index] = formatQuantity(total)
      }
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

// 获取计量单位map

const unitMap = ref<Map<number, string>>(new Map())
const getUnitMap = async () => {
  const res = await UnitApi.getUnitPage({
    pageNo: 1,
    pageSize: 100
  })
  unitMap.value = new Map(res.list.map((item: any) => [item.id, item.name]))
}

// 获取部门映射
const deptMap = ref<Map<number, string>>(new Map())
const getDeptMap = async () => {
  try {
    const res = await DeptApi.getSimpleDeptList()
    // 递归处理部门树，建立ID到名称的映射
    const buildDeptMap = (depts: any[]) => {
      depts.forEach(dept => {
        deptMap.value.set(dept.id, dept.name)
        if (dept.children && dept.children.length > 0) {
          buildDeptMap(dept.children)
        }
      })
    }
    buildDeptMap(res)
  } catch (error) {
    console.error('获取部门数据失败:', error)
  }
}
/** 初始化 **/
onMounted(async () => {
  await getList()
  await getUnitMap()
  await getDeptMap()
})
</script>

<style scoped lang="scss">
/* 需求编号容器样式 */
.request-no-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 0;
}

/* 需求编号文本样式 */
.request-no-text {
  font-weight: 500;
  color: #303133;
}

/* 订单号行布局 */
.order-no-cell {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
  gap: 5px; /* 文字和图标之间保持一点点间隔 */
}

.order-no-content {
  flex: 0 1 auto; /* 改为自适应宽度，不占满剩余空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 16px); /* 为图标预留空间 */
}

/* 复制按钮样式 */
.copy-btn {
  padding: 2px !important;
  height: 20px !important;
  min-height: 20px !important;
  width: 20px !important;
  font-size: 12px;
  opacity: 0.6;
  transition: opacity 0.2s;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}



.copy-btn:hover {
  opacity: 1;
}

/* 状态标签样式 */
.status-tag :deep(.el-tag) {
  font-size: 9px !important;
  padding: 1px 4px !important;
  height: 16px !important;
  line-height: 14px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  flex-shrink: 0;
}


</style>
