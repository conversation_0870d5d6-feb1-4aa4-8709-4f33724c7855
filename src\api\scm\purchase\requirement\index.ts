import request from '@/config/axios'

// 采购需求 VO
export interface RequirementVO {
  id: number // ID
  requestNo: string // 需求编号
  materialId: number // 物料ID
  materialName: string // 物料名称
  requirementDate: Date // 需求日期
  materialCode: string // 物料编号
  materialSpec: string // 物料规格
  materialType: string // 物料类型
  departmentId: string // 需求部门ID
  quantity: number // 物料数量
  unitPrice: number // 物料单价
  unit: string // 价格单位
  amount: number // 物料金额
  expectedDeliveryDate: Date // 期望交货日期
  status: string // 状态
  totalAmount: number // 总金额
  sourceType: string // 需求来源类型
  sourceNo: string // 来源单号
  bizOrderId: number // 业务订单ID
  bizOrderNo: string // 业务订单编号
  bizOrderTyp: string // 业务订单类型
  bizOrderItemId: number // 业务订单项ID
  remark: string // 备注
  note: string // 摘要
  purchaseNo: string // 采购订单
}

// 采购需求 API
export const RequirementApi = {
  // 查询采购需求分页
  getRequirementPage: async (params: any) => {
    return await request.get({ url: `/scm/purchase/requirement/page`, params })
  },

  // 查询采购需求详情
  getRequirement: async (id: number) => {
    return await request.get({ url: `/scm/purchase/requirement/get?id=` + id })
  },

  // 新增采购需求
  createRequirement: async (data: RequirementVO) => {
    return await request.post({ url: `/scm/purchase/requirement/create`, data })
  },

  // 修改采购需求
  updateRequirement: async (data: RequirementVO) => {
    return await request.put({ url: `/scm/purchase/requirement/update`, data }) // 增加 scm 前缀
  },

  // 删除采购需求
  deleteRequirement: async (id: number) => {
    return await request.delete({ url: `/scm/purchase/requirement/delete?id=` + id }) // 增加 scm 前缀
  },

  // 导出采购需求 Excel
  exportRequirement: async (params) => {
    return await request.download({ url: `/scm/purchase/requirement/export-excel`, params }) // 增加 scm 前缀 
  },
}
