<template>
  <ContentWrap>
    <!-- 分析结果标题区域 -->
    <div class="analysis-header">
      <div class="analysis-title">
        <span class="title-text">分析结果</span>
        <span class="analysis-info">共 <span class="highlight-count">{{ analysisList.length }}</span> 个订单, <span class="highlight-count">{{ analysisSummary.materialCount }}</span> 个物料项</span>
      </div>

      <!-- 统计卡片区域 -->
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-value">{{ analysisSummary.orderCount || 0 }}</div>
          <div class="stat-label">订单总数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ analysisSummary.materialCount || 0 }}</div>
          <div class="stat-label">物料总数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" :class="{'text-danger': analysisSummary.shortageCount > 0}">
            {{ analysisSummary.shortageCount || 0 }}
          </div>
          <div class="stat-label">缺料项数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-success">{{ analysisSummary.readyCount || 0 }}</div>
          <div class="stat-label">完全准备就绪</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-warning">{{ analysisSummary.partialReadyCount || 0 }}</div>
          <div class="stat-label">部分准备就绪</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-danger">{{ analysisSummary.notReadyCount || 0 }}</div>
          <div class="stat-label">未准备就绪</div>
        </div>
        <div class="stat-card">
          <div class="stat-value text-info">{{ analysisSummary.purchasedCount || 0 }}</div>
          <div class="stat-label">已转采购</div>
        </div>
      </div>

      <div class="header-actions">
        <el-button
          type="primary"
          size="small"
          @click="handleBack"
        >
          <el-icon><Back /></el-icon> 返回列表
        </el-button>
      </div>
    </div>

    <!-- 分析结果内容 -->
    <el-tabs model-value="resultDetail">
      <el-tab-pane label="分析详情" name="resultDetail">
        <el-table
          v-loading="loading"
          :data="flattenedList"
          :stripe="true"
          :show-overflow-tooltip="true"
          :span-method="objectSpanMethod"
          :border="false"
          max-height="450px"
          class="analysis-table">
          <el-table-column align="center" type="selection" width="60" />
          <el-table-column label="序号" align="center" prop="index" width="60" fixed="left">
            <template #default="scope">
              <!-- 只在合并组的第一行显示序号 -->
              <span v-if="groupIndexArr[scope.$index] > 0">{{ groupIndexArr[scope.$index] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品" align="center" prop="productCode" width="180" fixed="left">
            <template #default="scope">
              <span>{{scope.row.requestNo}}</span><br/>
              <span>{{ scope.row.orderNo }}</span><br/>
              <span>{{ scope.row.productCode }}</span><br/>
              <span>{{ scope.row.productName }}</span><br/>
              <span>{{ scope.row.spec }}</span><br/>
            </template>
          </el-table-column>
          <el-table-column label="需求数量" align="center" prop="pendingQuantity" width="100">
            <template #default="scope">
              <span>{{ scope.row.pendingQuantity }} {{ unitMap?.get(String(scope.row.productUnit)) || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="库存可生产" align="center" prop="readyQuantity" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.readyQuantity <= 0 ? 'danger' : 'success'"
                v-if="scope.row?.hasOwnProperty('readyQuantity')"
              >
                {{ scope.row.readyQuantity }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="准备状态" align="center" prop="readyStatus" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MFG_MATERIAL_READY_STATUS" :value="scope.row.readyStatus" />
            </template>
          </el-table-column>
          <el-table-column label="要求&备注" align="left" prop="requirement" width="100">
            <template #default="scope">
              <span v-if="scope.row.requirement">{{ scope.row.requirement }}</span>
              <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="序号" align="center" prop="detail.num" width="60"/>
          <el-table-column label="物料编码" align="left" prop="detail.materialCode" width="120"/>
          <el-table-column label="物料名称" align="left" prop="detail.materialName" width="150"/>
          <el-table-column label="需求数量" align="center" prop="detail.pendingQuantity" width="100">
            <template #default="scope">
              <span v-if="scope.row.detail?.pendingQuantity">
                {{ scope.row.detail.pendingQuantity }} {{ unitMap?.get(String(scope.row.detail?.unit)) || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="库存可用" align="center" prop="detail.readyQuantity" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.detail?.shortageQuantity < 0 ? 'danger' : 'success'"
                v-if="scope.row.detail?.hasOwnProperty('readyQuantity')"
              >
                {{ scope.row.detail?.readyQuantity }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="欠缺" align="center" prop="detail.shortageQuantity" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.detail?.shortageQuantity < 0 ? 'danger' : 'success'"
                v-if="scope.row.detail?.hasOwnProperty('shortageQuantity')"
              >
                {{ scope.row.detail?.shortageQuantity }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="准备状态" align="center" prop="detail.readyStatus" width="100">
            <template #default="scope">
              <dict-tag v-if="scope.row.detail?.readyStatus !== undefined" :type="DICT_TYPE.MFG_MATERIAL_READY_STATUS" :value="scope.row.detail.readyStatus" />
            </template>
          </el-table-column>
          <el-table-column label="可锁数量" align="center" prop="detail.lockStockQuantity" width="100" />
          <el-table-column label="在途可锁" align="center" prop="detail.lockTransitQuantity" width="100" />
          <el-table-column label="库存基本单位数量" align="center" prop="detail.stockQuantity" width="140"/>
          <el-table-column label="基本单位" align="center" prop="detail.standardUnitId" width="100">
            <template #default="scope">
              <span>{{ unitMap?.get(String(scope.row.detail?.standardUnitId)) || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="已转采购" align="center" prop="detail.purchaseQuantity" width="100">
            <template #default="scope">
<!--                <el-badge :value="getDictLabel(DICT_TYPE.COMMON_TASK_STATUS, scope.row.detail?.purchaseStatus)">-->
                  <el-tag :type="getPurchaseLabelType(scope.row.detail?.purchaseStatus)"  v-if="scope.row.detail?.purchaseQuantity != undefined">{{scope.row.detail?.purchaseQuantity}}</el-tag>
<!--                </el-badge>-->
            </template>
          </el-table-column>
          <el-table-column label="要求&备注" align="center" prop="detail.requirement" min-width="100">
            <template #default="scope">
              <span v-if="scope.row.detail?.requirement">{{ scope.row.detail.requirement }}</span>
              <span v-if="scope.row.detail?.remark">{{ scope.row.detail.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" width="100" fixed="right" prop="purchase">
            <template #default="scope">
              <el-button
                v-if="shouldShowTransferButton(scope.row.detail)"
                type="primary"
                size="small"
                link
                @click="handleTransferRequirement(scope.row)"
              >
                转采购需求
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" width="100" fixed="right" prop="workOrder">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                link
                @click="handleSingleOrderTransfer(scope.row)"
                v-if="scope.row.readyStatus !== 0 && scope.row.pendingQuantity > 0"
              >
                转生产
              </el-button>
              <el-tooltip
                v-else-if="scope.row.pendingQuantity === 0"
                content="已全部转生产"
                placement="top"
                effect="light"
              >
                <el-tag type="success">已全部转生产</el-tag>
              </el-tooltip>
              <el-tooltip
                v-else
                content="物料未准备完成，无法转生产"
                placement="top"
                effect="light"
              >
                <span class="text-gray-400 text-xs">未准备完成</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="分析总结" name="resultSummary">
        <!-- 缺料清单 -->
        <div v-if="analysisSummary.shortageCount > 0" class="shortage-list">
          <div class="section-title">
            <el-icon><Warning /></el-icon>
            <span>缺料清单</span>
          </div>

          <el-table
            :data="shortageList"
            border
            size="small"
            max-height="400"
            class="shortage-table">
            <el-table-column label="物料编码" prop="materialCode" width="120" />
            <el-table-column label="物料名称" prop="materialName" min-width="150" />
            <el-table-column label="缺料数量" width="100" align="center">
              <template #default="scope">
                <span class="text-danger font-bold">{{ scope.row.shortageQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="库存可用" prop="readyQuantity" width="100" align="center" />
            <el-table-column label="已转采购" width="100" align="center">
              <template #default="scope">
                <span>{{ scope.row.purchaseQuantity || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template #default="scope">
                <el-button
                  v-if="shouldShowSummaryTransferButton(scope.row)"
                  type="primary"
                  size="small"
                  link
                  @click="handleSummaryTransferRequirement(scope.row)"
                >
                  转采购
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 无缺料时的提示 -->
        <div v-else class="no-shortage">
          <el-empty description="暂无缺料项目" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ElIcon } from 'element-plus'
import { Back, Warning } from '@element-plus/icons-vue'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { computed, ref, watch } from 'vue'

// 定义 Props
interface Props {
  analysisList: any[]
  analysisSummary: {
    orderCount: number
    materialCount: number
    shortageCount: number
    readyCount: number
    partialReadyCount: number
    notReadyCount: number
    purchasedCount: number
  }
  unitMap: Map<string, string>
  loading: boolean
}

const props = withDefaults(defineProps<Props>(), {
  analysisList: () => [],
  analysisSummary: () => ({
    orderCount: 0,
    materialCount: 0,
    shortageCount: 0,
    readyCount: 0,
    partialReadyCount: 0,
    notReadyCount: 0,
    purchasedCount: 0
  }),
  unitMap: () => new Map(),
  loading: false
})

// 定义 Emits
const emit = defineEmits<{
  back: []
  transferRequirement: [row: any]
  transferWorkOrder: [row: any]
  summaryTransferRequirement: [material: any]
}>()

// ----------------- 处理合并表格的逻辑 start -----------------------
// 定义合并行数存储数组
const spanArr = ref<number[]>([])

// 增加用于存储组序号的数组
const groupIndexArr = ref<number[]>([])

// 计算扁平化后的列表数据
const calculateFlattenedList = () => {
  const result: any[] = []
  spanArr.value = [] // 合并行数存储数组
  groupIndexArr.value = [] // 组序号存储数组
  let groupIndex = 0 // 组序号计数器

  props.analysisList.forEach(order => {
    const details = order.details?.length ? order.details : [{}] // 确保无明细时也有占位行
    const detailCount = details.length
    let isFirstRowInGroup = true // 标记是否是组内第一行

    details.forEach((detail: any) => {
      result.push({ ...order, detail })

      // 主信息列只在第一个明细行合并
      if (isFirstRowInGroup) {
        spanArr.value.push(detailCount) // 合并行数 = 明细数量
        groupIndex++ // 递增组序号
        groupIndexArr.value.push(groupIndex) // 存储组序号
        isFirstRowInGroup = false
      } else {
        spanArr.value.push(0) // 0表示该行不显示主信息
        groupIndexArr.value.push(0) // 0表示该行不显示序号
      }
    })
  })
  return result
}

// 扁平化后的列表数据
const flattenedList = ref<any[]>([])

// 监听分析结果变化，更新扁平化列表
watch(() => props.analysisList, () => {
  flattenedList.value = calculateFlattenedList()
}, { immediate: true })

// 需要合并的主信息列（必须与el-table-column的prop严格匹配）
const mergeFields = [
  'index', 'orderNo', 'productCode', 'productName', 'spec', 'pendingQuantity',
  'readyQuantity', 'readyStatus', 'requirement', 'remark', 'workOrder'
]

// 处理表格合并单元格
const objectSpanMethod = ({ column, rowIndex }: { row: any, column: any, rowIndex: number }) => {
  // 处理选择列和主信息列合并
  if (column.type === 'selection' || mergeFields.includes(column.property)) {
    const span = spanArr.value[rowIndex]
    return {
      rowspan: span, // 合并行数
      colspan: span > 0 ? 1 : 0 // 0表示隐藏单元格
    }
  }
  // 商品明细列不合并
  return { rowspan: 1, colspan: 1 }
}
// ----------------- 处理合并表格的逻辑 end -----------------------

// 缺料清单
const shortageList = computed(() => {
  const result: any[] = []
  const materialMap = new Map()

  props.analysisList.forEach(order => {
    order.details?.forEach((detail: any) => {
      if (detail.shortageQuantity < 0) {
        const key = detail.materialId
        // 使用转换后的采购数量（如果存在）
        const effectivePurchaseQuantity = detail.convertedPurchaseQuantity !== undefined
          ? detail.convertedPurchaseQuantity
          : (detail.purchaseQuantity || 0)

        if (materialMap.has(key)) {
          const existing = materialMap.get(key)
          existing.shortageQuantity += detail.shortageQuantity
          existing.purchaseQuantity += effectivePurchaseQuantity
          existing.pendingQuantity += (detail.pendingQuantity || 0)
        } else {
          materialMap.set(key, {
            materialId: detail.materialId,
            materialCode: detail.materialCode,
            materialName: detail.materialName,
            materialType: detail.materialType,
            shortageQuantity: detail.shortageQuantity,
            readyQuantity: detail.readyQuantity,
            unit: detail.unit,
            standardUnitId: detail.standardUnitId,
            purchaseQuantity: effectivePurchaseQuantity,
            pendingQuantity: detail.pendingQuantity || 0,
          })
        }
      }
    })
  })

  materialMap.forEach(item => {
    result.push(item)
  })

  return result
})

// 事件处理函数
const handleBack = () => {
  emit('back')
}

const handleTransferRequirement = (row: any) => {
  emit('transferRequirement', row)
}

const handleSingleOrderTransfer = (row: any) => {
  emit('transferWorkOrder', row)
}

const handleSummaryTransferRequirement = (material: any) => {
  emit('summaryTransferRequirement', material)
}

const getPurchaseLabelType = (purchaseStatus: number) => {
  switch (purchaseStatus) {
    case 4:
      return 'success'
    case 1:
      return 'success'
    case 2:
      return 'danger'
    case 3:
      return 'warning'
    default:
      return 'danger'
  }
}



// 判断是否应该显示转采购需求按钮（同步版本，基于已有数据）
const shouldShowTransferButton = (detail: any) => {
  if (!detail?.materialId || detail.shortageQuantity >= 0) {
    return false
  }

  // 检查采购状态
  const purchaseStatus = detail.purchaseStatus || 0
  if (![0, 2, 3].includes(purchaseStatus)) {
    return false
  }

  const purchaseQuantity = detail.purchaseQuantity || 0
  const shortageQuantity = Math.abs(detail.shortageQuantity || 0)
  const pendingQuantity = detail.pendingQuantity || 0

  // 如果没有采购数量，直接显示按钮
  if (purchaseQuantity === 0) {
    return true
  }

  // 优先使用转换后的采购数量进行比较（如果存在）
  const effectivePurchaseQuantity = detail.convertedPurchaseQuantity !== undefined
    ? detail.convertedPurchaseQuantity
    : purchaseQuantity



  // 使用转换后的数量进行比较
  return effectivePurchaseQuantity < shortageQuantity && effectivePurchaseQuantity < pendingQuantity
}

// 判断是否应该显示分析总结tab中的转采购按钮
const shouldShowSummaryTransferButton = (material: any) => {
  const purchaseQuantity = material.purchaseQuantity || 0
  const shortageQuantity = Math.abs(material.shortageQuantity || 0)
  const pendingQuantity = material.pendingQuantity || 0

  // 如果没有采购数量，直接显示按钮
  if (purchaseQuantity === 0) {
    return true
  }

  // 使用原来的逻辑进行比较
  // 注意：这里同样可能存在单位不一致的问题
  return purchaseQuantity < shortageQuantity && purchaseQuantity < pendingQuantity
}
</script>

<style lang="scss" scoped>
// 分析头部样式
.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f9fafc;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .analysis-title {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    .title-text {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 4px;
    }

    .analysis-info {
      font-size: 14px;
      color: #606266;

      .highlight-count {
        color: #409EFF;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }

  // 统计卡片样式
  .stats-cards {
    display: flex;
    gap: 8px;
    margin: 0 20px;
    flex: 1;
    justify-content: center;

    .stat-card {
      min-width: 70px;
      padding: 8px 12px;
      text-align: center;
      background-color: #ffffff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .stat-value {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 2px;
        color: #303133;

        &.text-success {
          color: #67c23a;
        }

        &.text-warning {
          color: #e6a23c;
        }

        &.text-danger {
          color: #f56c6c;
        }

        &.text-info {
          color: #409eff;
        }
      }

      .stat-label {
        font-size: 11px;
        color: #909399;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .el-button {
      margin-left: 10px;
    }
  }
}

// 分析表格样式
.analysis-table {
  margin-bottom: 15px;

  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table--small) {
    font-size: 13px;
  }

  :deep(.el-table__body-wrapper) {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
    }
  }
}

// 缺料清单样式
.shortage-list {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 16px;

  .el-icon {
    margin-right: 8px;
    color: #f56c6c;
  }
}

.shortage-table {
  :deep(.el-table__header) {
    background-color: #fff2f2;
  }
}

// 无缺料时的样式
.no-shortage {
  padding: 40px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.font-bold {
  font-weight: bold;
}
</style>
